//
//  CloudImageView.swift
//  practice
//
//  Created by <PERSON> on 2025/5/13.
//

import SwiftUI

struct CloudImageView: View {
    let path: String
    @EnvironmentObject var imageManager: ImageManager
    @State private var image: UIImage?

    var body: some View {
        Group {
            if let image = image {
                Image(uiImage: image)
                    .resizable()
            } else {
                ProgressView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.clear)
                    .contentShape(Rectangle())
            }
        }
        .onAppear {
            // 首先尝试从缓存同步加载
            if image == nil {
                if let cachedImage = imageManager.loadImage(path: path) {
                    self.image = cachedImage
                }
            }
        }
        .task {
            // 如果缓存中没有，则异步加载
            if image == nil {
                self.image = await imageManager.loadImageAsync(path: path)
            }
        }
    }
}
