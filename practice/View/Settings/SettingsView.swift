//
//  SettingsView.swift
//  practice
//
//  Created by 陈昇 on 4/12/24.
//

import SwiftUI
import SwiftData

struct SettingsView: View {
    @EnvironmentObject var navManager: NavigationManager
    @StateObject private var taskListManager: TaskListManager = .shared
    private var membershipManager: MembershipManager = .shared
    @StateObject private var colorThemeManager = ColorThemeManager.shared
    @StateObject private var notificationManager = NotificationManager.shared
    
//    var defaultTask: TaskItem? {
//        taskListManager.getDefaultTask()
//    }
    
    @State private var showPracticeTimeEditPage = false
    @State private var showPracticeTimeGoalPicker = false
    @State private var showNotificationTimePicker = false
    @State private var showRingtoneSelector = false

    @FocusState private var taskNameIsFocus:Bool
    @State var goalMinute = 0

    @State private var showMemberShipSubscription = false

//    // MARK: Backup system
//    @StateObject private var backupManager = BackupManager()
//    @State private var showBackupProgress = false
//    @State private var showImportProgress = false

    // MARK: Config data
    @State private var personalConfig: PersonalConfig?

    private let themeSelections = [String(localized: "system"), String(localized: "light"), String(localized: "dark")]
    
    var userLanguage: String {
        let locale = Locale.autoupdatingCurrent
        guard let primaryLanguage = locale.language.languageCode?.identifier else { return "" }
        return NSLocale.current.localizedString(forLanguageCode: primaryLanguage) ?? ""
    }
    
    var body: some View {
        List {
            Section(
                header: Text("Membership"),
                footer: Text(membershipManager.status.description)
                    .font(.caption2)
                    .foregroundStyle(.secondary.opacity(0.8)),
                content: {
                    HStack(alignment: .top) {
                        SectionImageWrapper(systemImageName: "crown.fill")
                            .foregroundColor(.brown)
                        Text("Only Practice Pro")
                        Spacer()
                        Text(membershipManager.status.localizedString)
                            .foregroundColor(.secondary)
                    }
                    .frame(height: 100)
                    .contentShape(Rectangle())
                    .background {
                        HStack {
                            Spacer()
                            Image("Violinist")
                                .resizable()
                                .scaledToFit()
                                .frame(height: 280)
                                .opacity(0.2)
                                .offset(y: 90)
                        }
                    }
                    .onTapGesture {
//                        TODO is member and disable
                        if membershipManager.status == .free {
                            showMemberShipSubscription = true
                        }
                    }
                }
            )
            Section("System") {
                HStack {
                    Picker(selection: $colorThemeManager.selectedTheme, content: {
                        ForEach(themeSelections, id: \.self) {text in
                            Text(text)
                        }
                    }, label: {
                        HStack {
                            SectionImageWrapper(systemImageName: "paintpalette.fill")
                                .foregroundStyle(.accent)
                            Text("Theme")
                        }
                    })
                    .pickerStyle(.navigationLink)
                }
                
                HStack {
                    SectionImageWrapper(systemImageName: "globe.asia.australia.fill")
                        .foregroundStyle(.blue.gradient)
                    Text("Language")
                    Spacer()
                    Text(userLanguage)
                        .foregroundColor(.secondary)
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if let url = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(url)
                    }
                }
            }
            
            Section("Practice Goal") {
                HStack {
                    SectionImageWrapper(systemImageName: "target")
                        .foregroundColor(.blue)
                    Text("Daily Practicing Goal")
                    Spacer()
                    Text("\(getDailyGoal()) min / day")
                        .foregroundColor(.secondary)
                    Image(systemName: "arrowtriangle.down.fill")
                        .font(.caption)
                        .foregroundColor(.accentColor)
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    showPracticeTimeGoalPicker = true
                }
            }
            
            Section("Notifications") {
                Toggle(isOn: $notificationManager.dailyReminderEnabled) {
                    HStack {
                        SectionImageWrapper(systemImageName: "bell.fill")
                            .foregroundColor(.orange)
                        Text("Daily Reminder")
                    }
                }
                .onChange(of: notificationManager.dailyReminderEnabled) { oldValue, newValue in
                    if newValue {
                        notificationManager.requestNotificationPermission { granted in
                            if granted {
                                notificationManager.scheduleDailyReminder()
                            } else {
                                notificationManager.dailyReminderEnabled = false
                            }
                        }
                    }
                }
                
                // Reminder Time Picker
                if notificationManager.dailyReminderEnabled {
                    HStack {
                        SectionImageWrapper(systemImageName: "clock.fill")
                            .foregroundColor(.blue)
                        Text("Reminder Time")
                        Spacer()
                        Text(notificationManager.reminderTime, formatter: shortTimeFormatter)
                            .foregroundColor(.secondary)
                        Image(systemName: "arrowtriangle.down.fill")
                            .font(.caption)
                            .foregroundColor(.accentColor)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        showNotificationTimePicker = true
                    }
                    .disabled(!notificationManager.dailyReminderEnabled)
                    .opacity(notificationManager.dailyReminderEnabled ? 1.0 : 0.6)
                }
            }

            Section("Countdown Alerts") {
                Toggle(isOn: $notificationManager.countdownSoundEnabled) {
                    HStack {
                        SectionImageWrapper(systemImageName: "alarm.waves.left.and.right.fill")
                            .foregroundColor(.green)
                        Text("Countdown Sound")
                    }
                }

                if (notificationManager.countdownSoundEnabled) {
                    // 选择铃声
                    HStack {
                        SectionImageWrapper(systemImageName: "music.note")
                            .foregroundColor(.purple)
                        Text("Ringtone")
                        Spacer()
                        Text(notificationManager.selectedRingtone.displayName)
                            .foregroundColor(.secondary)
                        Image(systemName: "arrowtriangle.down.fill")
                            .font(.caption)
                            .foregroundColor(.accentColor)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        showRingtoneSelector = true
                    }
                    .disabled(!notificationManager.countdownSoundEnabled)
                    .opacity(notificationManager.countdownSoundEnabled ? 1.0 : 0.6)
                    
                    Toggle(isOn: $notificationManager.playInSilentMode) {
                        HStack {
                            SectionImageWrapper(systemImageName: "speaker.slash.fill")
                                .foregroundColor(.orange)
                            Text("Play in Silent Mode")
                        }
                    }
                    .disabled(!notificationManager.countdownSoundEnabled)
                    .opacity(notificationManager.countdownSoundEnabled ? 1.0 : 0.6)
                }
                    
//                HStack {
//                    Toggle(isOn: $notificationManager.countdownVibrationEnabled) {
//                        HStack {
//                            SectionImageWrapper(systemImageName: "iphone.radiowaves.left.and.right")
//                                .foregroundColor(.red)
//                            Text("Countdown Vibration")
//                        }
//                    }
//
//                    if notificationManager.countdownVibrationEnabled {
//                        Button(action: {
//                            notificationManager.triggerVibration()
//                        }) {
//                            Image(systemName: "play.circle")
//                                .foregroundColor(.blue)
//                                .font(.title2)
//                        }
//                        .buttonStyle(PlainButtonStyle())
//                    }
//                }
//
//                // 测试震动按钮 (5秒后触发)
//                if notificationManager.countdownVibrationEnabled {
//                    Button(action: {
//                        testVibrationAfterDelay()
//                    }) {
//                        HStack {
//                            SectionImageWrapper(systemImageName: "timer")
//                                .foregroundColor(.purple)
//                            Text("Test Vibration (5s)")
//                            Spacer()
//                            Image(systemName: "play.circle")
//                                .foregroundColor(.purple)
//                        }
//                    }
//                    .disabled(!notificationManager.countdownVibrationEnabled)
//                    .opacity(notificationManager.countdownVibrationEnabled ? 1.0 : 0.6)
//                }
//
//                // Critical Alert 权限检查按钮
//                Button(action: {
//                    checkCriticalAlertPermission()
//                }) {
//                    HStack {
//                        SectionImageWrapper(systemImageName: "exclamationmark.triangle")
//                            .foregroundColor(.orange)
//                        Text("Check Critical Alert Permission")
//                        Spacer()
//                        Image(systemName: "checkmark.shield")
//                            .foregroundColor(.orange)
//                    }
//                }
//
//                // 测试直接音频播放按钮
//                Button(action: {
//                    testDirectAudioPlayback()
//                }) {
//                    HStack {
//                        SectionImageWrapper(systemImageName: "speaker.wave.3")
//                            .foregroundColor(.green)
//                        Text("Test Direct Audio Playback")
//                        Spacer()
//                        Image(systemName: "play.circle")
//                            .foregroundColor(.green)
//                    }
//                }
//                .disabled(!notificationManager.countdownSoundEnabled)
//                .opacity(notificationManager.countdownSoundEnabled ? 1.0 : 0.6)
//
//                // 测试通知按钮
//                Button(action: {
//                    testNotification()
//                }) {
//                    HStack {
//                        SectionImageWrapper(systemImageName: "bell.badge")
//                            .foregroundColor(.blue)
//                        Text("Test Notification (5s)")
//                        Spacer()
//                        Image(systemName: "play.circle")
//                            .foregroundColor(.blue)
//                    }
//                }
//                .disabled(!notificationManager.countdownSoundEnabled)
//                .opacity(notificationManager.countdownSoundEnabled ? 1.0 : 0.6)
            }
//
//            Section("Data Backup") {
//                HStack {
//                    SectionImageWrapper(systemImageName: "externaldrive.badge.plus")
//                        .foregroundColor(.blue)
//                    Text("Backup Data")
//                    Spacer()
//                    Image(systemName: "chevron.right")
//                        .font(.caption)
//                        .foregroundColor(.secondary)
//                }
//                .contentShape(Rectangle())
//                .onTapGesture {
//                    showBackupProgress = true
//                }
//
//                HStack {
//                    SectionImageWrapper(systemImageName: "square.and.arrow.down")
//                        .foregroundColor(.green)
//                    Text("Import from Backup")
//                    Spacer()
//                    Image(systemName: "chevron.right")
//                        .font(.caption)
//                        .foregroundColor(.secondary)
//                }
//                .contentShape(Rectangle())
//                .onTapGesture {
//                    showImportProgress = true
//                }
//            }

            Section("Contact Developer") {
                HStack {
                    SectionImageWrapper(systemImageName: "envelope")
                        .foregroundColor(.blue)
                    Text("Email")
                    Spacer()
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if let url = URL(string: "mailto:<EMAIL>") {
                        UIApplication.shared.open(url)
                    }
                }
                HStack {
                    SectionImageWrapper(imageName: "xhslogo")
                    Text("Red Note")
                    Spacer()
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    openXiaohongshuProfile()
                }
            }
        }
        .navigationTitle("Settings")
        .sheet(isPresented: $showPracticeTimeGoalPicker) {
                goalTimeSelector
                    .presentationDetents([.medium])
                    .navigationBarTitleDisplayMode(.inline)
//            .presentationDetents([.medium])
        }
        .sheet(isPresented: $showMemberShipSubscription) {
            CustomSubscribeView()
        }
        .sheet(isPresented: $showNotificationTimePicker) {
            ReminderTimeSelector(reminderTime: $notificationManager.reminderTime)
                .presentationDetents([.medium])
        }
        .sheet(isPresented: $showRingtoneSelector) {
            RingtoneSelector(selectedRingtone: $notificationManager.selectedRingtone)
                .presentationDetents([.medium])
        }
//        .fullScreenCover(isPresented: $showBackupProgress) {
//            BackupProgressView(
//                backupManager: backupManager,
//                isPresented: $showBackupProgress
//            )
//        }
//        .fullScreenCover(isPresented: $showImportProgress) {
//            ImportProgressView(
//                backupManager: backupManager,
//                isPresented: $showImportProgress
//            )
//        }
        .onAppear() {
            let totalFiles = iCloudDocumentManager.shared.fileCount()
            print("totalFiles", totalFiles)
            Task {
                await loadConfigData()
            }
        }
    }

    // MARK: - Config Management
    @MainActor
    private func loadConfigData() async {
        let configManager = ConfigManager.shared
        personalConfig = configManager.personalConfig
        goalMinute = getDailyGoal()
    }

    private func getDailyGoal() -> Int {
        if let config = personalConfig {
            return (config.dailyPracticeGoal ?? 0) / 60
        }
        return 0
    }

    @MainActor
    private func updateDailyGoal(_ minutes: Int) async {
        let configManager = ConfigManager.shared
        configManager.updateDailyGoal(minutes)
        // Update local state
        await loadConfigData()
    }

    func openXiaohongshuProfile() {
        let userID = "639d0cf8000000002601147e"
        
        // 1. 尝试打开小红书 App
        if let appURL = URL(string: "xhsdiscover://user/\(userID)"),
           UIApplication.shared.canOpenURL(appURL) {
            UIApplication.shared.open(appURL, options: [:], completionHandler: nil)
        
        // 2. fallback 到网页
        } else if let webURL = URL(string: "https://www.xiaohongshu.com/user/profile/\(userID)") {
            UIApplication.shared.open(webURL, options: [:], completionHandler: nil)
        }
    }

//    func testNotification() {
//        print("🧪 Test notification button pressed")
//
//        // 首先检查系统通知设置
//        notificationManager.checkSystemNotificationSettings()
//
//        // 请求权限
//        notificationManager.requestNotificationPermission { granted in
//            if granted {
//                print("🧪 Permission granted, scheduling test notification")
//
//                // 再次检查详细设置
//                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
//                    self.notificationManager.checkSystemNotificationSettings()
//                }
//
//                // 调度一个5秒后的测试通知
//                notificationManager.scheduleCountdownNotification(
//                    identifier: "testNotification",
//                    title: "Test Notification",
//                    body: "This is a test notification to verify sound and vibration settings.",
//                    timeInterval: 5.0
//                )
//
//                // 10秒后检查已送达的通知
//                DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
//                    notificationManager.checkDeliveredNotifications()
//                }
//
//            } else {
//                print("🧪 Permission denied, cannot schedule test notification")
//            }
//        }
//    }

//    func testDirectAudioPlayback() {
//        print("🎵 Direct audio playback test button pressed")
//
//        // 同时测试震动
//        if notificationManager.countdownVibrationEnabled {
//            DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
//                self.notificationManager.playCountdownFinishedSound(at: TimeInterval(10))
//            }
//        }
//    }
    
    @ViewBuilder
    private var goalTimeSelector: some View {
        GoalTimeSelectViewForSetting(goalMinute: $goalMinute)
            .onAppear {
                goalMinute = getDailyGoal()
            }
            .onDisappear {
                Task {
                    await updateDailyGoal(goalMinute)
                }
            }
    }
    
    struct TextFieldBindable: View {
        @Bindable var taskItem: TaskItem
        var body: some View {
            TextField("Default Task Name", text: $taskItem.pieceName)
                .multilineTextAlignment(.trailing)
        }
    }
    
    struct SectionImageWrapper: View {
        @Environment(\.sizeCategory) var sizeCategory
        var systemImageName: String = "crown.fill"
        var imageName: String?
        
        var body: some View {
            let baseFont = UIFont.systemFont(ofSize: 17) // 或你自定义的 base font
            let scaledFontSize = UIFontMetrics(forTextStyle: .body).scaledFont(for: baseFont).pointSize
            VStack(alignment: .leading) {
                if let imageName = imageName {
                    Image(imageName)
                        .resizable()
                        .scaledToFit()
                        .frame(width: scaledFontSize, height: scaledFontSize)
                        .background(.white)
                        .cornerRadius(4)
                } else {
                    Image(systemName: systemImageName)
                        .renderingMode(.original)
                }
            }
            .frame(width: 1.5 * scaledFontSize)
        }
    }
}

#Preview {
    SettingsView()
        .environmentObject(NavigationManager())
}
